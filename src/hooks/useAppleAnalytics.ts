import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/supabase/client/client';

// Types for Apple Analytics data from new table
export interface AppleAnalyticsRecord {
  unique_id: string;
  date: string;
  app_name: string;
  app_apple_identifier: string;
  event: 'Install' | 'Delete';
  app_version: string;
  device: string;
  platform_version: string;
  source_type: string;
  territory: string;
  counts: number;
  unique_devices: number;
  download_type: string | null;
  page_type: string;
  app_download_date: string | null;
  created_at: string;
  updated_at: string;
}

// Note: Daily summary and session analytics are no longer available from the new table
// These interfaces are kept for backward compatibility but will return empty arrays

export interface AppleAnalyticsMetrics {
  totalInstalls: number;
  totalUninstalls: number;
  netInstalls: number;
  firstTimeDownloads: number;
  updates: number;
  restores: number;
  redownloads: number;
  topTerritories: Array<{ territory: string; installs: number; uninstalls: number }>;
  topSourceTypes: Array<{ source_type: string; installs: number; uninstalls: number }>;
  topSourceApps: Array<{ source_app: string; installs: number; uninstalls: number }>;
  dailyTrends: Array<{ date: string; installs: number; uninstalls: number }>;
  deviceBreakdown: Array<{ device_type: string; installs: number; uninstalls: number }>;
  versionBreakdown: Array<{ app_version: string; installs: number; uninstalls: number }>;
  platformVersions: Array<{ platform_version: string; installs: number; uninstalls: number }>;
}

export interface AppleAnalyticsData {
  records: AppleAnalyticsRecord[];
  metrics: AppleAnalyticsMetrics;
  dailySummary: any[]; // Empty array for backward compatibility
  sessionAnalytics: any[]; // Empty array for backward compatibility
  dateRange: {
    earliest: string;
    latest: string;
  };
}

interface UseAppleAnalyticsOptions {
  timeRange?: string;
  startDate?: string;
  endDate?: string;
  autoFetch?: boolean;
}

interface UseAppleAnalyticsReturn {
  data: AppleAnalyticsData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

// Helper function to process raw data into metrics
function processAppleAnalyticsData(records: AppleAnalyticsRecord[]): AppleAnalyticsMetrics {
  const installRecords = records.filter(r => r.event === 'Install');
  const uninstallRecords = records.filter(r => r.event === 'Delete');

  // Basic totals
  const totalInstalls = installRecords.reduce((sum, r) => sum + r.counts, 0);
  const totalUninstalls = uninstallRecords.reduce((sum, r) => sum + r.counts, 0);
  const netInstalls = totalInstalls - totalUninstalls;

  // Install subtypes based on download_type
  const firstTimeDownloads = installRecords
    .filter(r => r.download_type === 'First-time download')
    .reduce((sum, r) => sum + r.counts, 0);

  const updates = installRecords
    .filter(r => r.download_type === 'Manual update' || r.download_type === 'Auto-download')
    .reduce((sum, r) => sum + r.counts, 0);

  const restores = installRecords
    .filter(r => r.download_type === 'Restore')
    .reduce((sum, r) => sum + r.counts, 0);

  const redownloads = installRecords
    .filter(r => r.download_type === 'Redownload')
    .reduce((sum, r) => sum + r.counts, 0);

  // Territory breakdown
  const territoryMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!territoryMap.has(r.territory)) {
      territoryMap.set(r.territory, { installs: 0, uninstalls: 0 });
    }
    const entry = territoryMap.get(r.territory)!;
    if (r.event === 'Install') {
      entry.installs += r.counts;
    } else {
      entry.uninstalls += r.counts;
    }
  });

  const topTerritories = Array.from(territoryMap.entries())
    .map(([territory, counts]) => ({ territory, ...counts }))
    .sort((a, b) => b.installs - a.installs)
    .slice(0, 10);

  // Source type breakdown
  const sourceTypeMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!sourceTypeMap.has(r.source_type)) {
      sourceTypeMap.set(r.source_type, { installs: 0, uninstalls: 0 });
    }
    const entry = sourceTypeMap.get(r.source_type)!;
    if (r.event === 'Install') {
      entry.installs += r.counts;
    } else {
      entry.uninstalls += r.counts;
    }
  });

  const topSourceTypes = Array.from(sourceTypeMap.entries())
    .map(([source_type, counts]) => ({ source_type, ...counts }))
    .sort((a, b) => b.installs - a.installs);

  // Source app breakdown - Note: new table doesn't have source_app field
  // We'll use page_type as a proxy for different acquisition sources
  const sourceAppMap = new Map<string, { installs: number; uninstalls: number }>();
  records.filter(r => r.page_type && r.page_type !== 'No page').forEach(r => {
    const sourceApp = r.page_type;
    if (!sourceAppMap.has(sourceApp)) {
      sourceAppMap.set(sourceApp, { installs: 0, uninstalls: 0 });
    }
    const entry = sourceAppMap.get(sourceApp)!;
    if (r.event === 'Install') {
      entry.installs += r.counts;
    } else {
      entry.uninstalls += r.counts;
    }
  });

  const topSourceApps = Array.from(sourceAppMap.entries())
    .map(([source_app, counts]) => ({ source_app, ...counts }))
    .sort((a, b) => b.installs - a.installs)
    .slice(0, 10);

  // Daily trends
  const dailyMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!dailyMap.has(r.date)) {
      dailyMap.set(r.date, { installs: 0, uninstalls: 0 });
    }
    const entry = dailyMap.get(r.date)!;
    if (r.event === 'Install') {
      entry.installs += r.counts;
    } else {
      entry.uninstalls += r.counts;
    }
  });

  const dailyTrends = Array.from(dailyMap.entries())
    .map(([date, counts]) => ({ date, ...counts }))
    .sort((a, b) => a.date.localeCompare(b.date));

  // Device breakdown
  const deviceMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!deviceMap.has(r.device)) {
      deviceMap.set(r.device, { installs: 0, uninstalls: 0 });
    }
    const entry = deviceMap.get(r.device)!;
    if (r.event === 'Install') {
      entry.installs += r.counts;
    } else {
      entry.uninstalls += r.counts;
    }
  });

  const deviceBreakdown = Array.from(deviceMap.entries())
    .map(([device_type, counts]) => ({ device_type, ...counts }))
    .sort((a, b) => b.installs - a.installs);

  // Version breakdown
  const versionMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!versionMap.has(r.app_version)) {
      versionMap.set(r.app_version, { installs: 0, uninstalls: 0 });
    }
    const entry = versionMap.get(r.app_version)!;
    if (r.event === 'Install') {
      entry.installs += r.counts;
    } else {
      entry.uninstalls += r.counts;
    }
  });

  const versionBreakdown = Array.from(versionMap.entries())
    .map(([app_version, counts]) => ({ app_version, ...counts }))
    .sort((a, b) => b.installs - a.installs);

  // Platform version breakdown
  const platformVersionMap = new Map<string, { installs: number; uninstalls: number }>();
  records.forEach(r => {
    if (!platformVersionMap.has(r.platform_version)) {
      platformVersionMap.set(r.platform_version, { installs: 0, uninstalls: 0 });
    }
    const entry = platformVersionMap.get(r.platform_version)!;
    if (r.event === 'Install') {
      entry.installs += r.counts;
    } else {
      entry.uninstalls += r.counts;
    }
  });

  const platformVersions = Array.from(platformVersionMap.entries())
    .map(([platform_version, counts]) => ({ platform_version, ...counts }))
    .sort((a, b) => b.installs - a.installs);

  return {
    totalInstalls,
    totalUninstalls,
    netInstalls,
    firstTimeDownloads,
    updates,
    restores,
    redownloads,
    topTerritories,
    topSourceTypes,
    topSourceApps,
    dailyTrends,
    deviceBreakdown,
    versionBreakdown,
    platformVersions,
  };
}

export function useAppleAnalytics(options: UseAppleAnalyticsOptions = {}): UseAppleAnalyticsReturn {
  const { timeRange = 'all', startDate, endDate, autoFetch = true } = options;

  const [data, setData] = useState<AppleAnalyticsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAppleAnalytics = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      let query = supabase
        .from('apple_installation_deletion_data')
        .select('*')
        .order('date', { ascending: false });

      // Apply date filters based on timeRange or explicit dates
      if (startDate && endDate) {
        query = query.gte('date', startDate).lte('date', endDate);
      } else if (timeRange !== 'all') {
        const now = new Date();
        let filterDate: Date;

        switch (timeRange) {
          case '7d':
            filterDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case '30d':
            filterDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case '90d':
            filterDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          default:
            filterDate = new Date(0); // All time
        }

        if (timeRange !== 'all') {
          query = query.gte('date', filterDate.toISOString().split('T')[0]);
        }
      }

      const { data: records, error: fetchError } = await query;

      if (fetchError) {
        throw new Error(`Failed to fetch Apple Analytics data: ${fetchError.message}`);
      }

      // Note: Daily summary and session analytics are no longer available
      // These tables have been deprecated and removed
      const dailySummary: any[] = [];
      const sessionAnalytics: any[] = [];

      if (!records || records.length === 0) {
        setData({
          records: [],
          metrics: {
            totalInstalls: 0,
            totalUninstalls: 0,
            netInstalls: 0,
            firstTimeDownloads: 0,
            updates: 0,
            restores: 0,
            redownloads: 0,
            topTerritories: [],
            topSourceTypes: [],
            topSourceApps: [],
            dailyTrends: [],
            deviceBreakdown: [],
            versionBreakdown: [],
            platformVersions: [],
          },
          dailySummary: dailySummary || [],
          sessionAnalytics: sessionAnalytics || [],
          dateRange: { earliest: '', latest: '' }
        });
        return;
      }

      // Process the data
      const metrics = processAppleAnalyticsData(records);

      // Calculate date range
      const dates = records.map(r => r.date).sort();
      const dateRange = {
        earliest: dates[0] || '',
        latest: dates[dates.length - 1] || ''
      };

      setData({
        records,
        metrics,
        dailySummary: dailySummary || [],
        sessionAnalytics: sessionAnalytics || [],
        dateRange
      });

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch Apple Analytics data';
      setError(errorMessage);
      console.error('Error fetching Apple Analytics data:', err);
    } finally {
      setLoading(false);
    }
  }, [timeRange, startDate, endDate]);

  useEffect(() => {
    if (autoFetch) {
      fetchAppleAnalytics();
    }
  }, [fetchAppleAnalytics, autoFetch]);

  return {
    data,
    loading,
    error,
    refetch: fetchAppleAnalytics,
  };
}
